"""
Image Preprocessing Module
图像预处理模块

提供图像预处理功能，包括光照归一化、直方图均衡、Gamma校正等。
"""

import cv2
import numpy as np
from typing import Tuple, Optional, Union
import logging

logger = logging.getLogger(__name__)


class ImagePreprocessor:
    """图像预处理器"""
    
    def __init__(self, config: dict):
        """
        初始化图像预处理器
        
        Args:
            config: 预处理配置字典
        """
        self.config = config
        self.preprocessing_config = config.get('preprocessing', {})
        
    def preprocess(self, image: np.ndarray) -> np.ndarray:
        """
        对图像进行预处理
        
        Args:
            image: 输入图像 (BGR或灰度)
            
        Returns:
            预处理后的图像
        """
        # 转换为灰度图像
        if len(image.shape) == 3:
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        else:
            gray = image.copy()
            
        # 光照归一化
        if self.preprocessing_config.get('illumination', {}).get('enable', True):
            gray = self._normalize_illumination(gray)
            
        # 图像缩放
        if self.preprocessing_config.get('resize', {}).get('enable', False):
            gray = self._resize_image(gray)
            
        return gray
        
    def _normalize_illumination(self, image: np.ndarray) -> np.ndarray:
        """
        光照归一化
        
        Args:
            image: 输入灰度图像
            
        Returns:
            归一化后的图像
        """
        illum_config = self.preprocessing_config.get('illumination', {})
        method = illum_config.get('method', 'CLAHE')
        
        if method == 'CLAHE':
            return self._apply_clahe(image, illum_config.get('clahe', {}))
        elif method == 'histogram_eq':
            return self._apply_histogram_equalization(image)
        elif method == 'gamma':
            return self._apply_gamma_correction(image, illum_config.get('gamma', {}))
        else:
            logger.warning(f"Unknown illumination method: {method}")
            return image
            
    def _apply_clahe(self, image: np.ndarray, clahe_config: dict) -> np.ndarray:
        """
        应用CLAHE (Contrast Limited Adaptive Histogram Equalization)
        
        Args:
            image: 输入图像
            clahe_config: CLAHE配置
            
        Returns:
            处理后的图像
        """
        clip_limit = clahe_config.get('clipLimit', 2.0)
        tile_grid_size = tuple(clahe_config.get('tileGridSize', [8, 8]))
        
        clahe = cv2.createCLAHE(clipLimit=clip_limit, tileGridSize=tile_grid_size)
        return clahe.apply(image)
        
    def _apply_histogram_equalization(self, image: np.ndarray) -> np.ndarray:
        """
        应用直方图均衡化
        
        Args:
            image: 输入图像
            
        Returns:
            处理后的图像
        """
        return cv2.equalizeHist(image)
        
    def _apply_gamma_correction(self, image: np.ndarray, gamma_config: dict) -> np.ndarray:
        """
        应用Gamma校正
        
        Args:
            image: 输入图像
            gamma_config: Gamma配置
            
        Returns:
            处理后的图像
        """
        gamma = gamma_config.get('gamma_value', 1.2)
        
        # 构建查找表
        inv_gamma = 1.0 / gamma
        table = np.array([((i / 255.0) ** inv_gamma) * 255 for i in np.arange(0, 256)]).astype("uint8")
        
        # 应用Gamma校正
        return cv2.LUT(image, table)
        
    def _resize_image(self, image: np.ndarray) -> np.ndarray:
        """
        调整图像大小
        
        Args:
            image: 输入图像
            
        Returns:
            调整大小后的图像
        """
        resize_config = self.preprocessing_config.get('resize', {})
        max_width = resize_config.get('max_width', 1024)
        max_height = resize_config.get('max_height', 768)
        
        h, w = image.shape[:2]
        
        # 计算缩放比例
        scale_w = max_width / w
        scale_h = max_height / h
        scale = min(scale_w, scale_h, 1.0)  # 不放大图像
        
        if scale < 1.0:
            new_w = int(w * scale)
            new_h = int(h * scale)
            return cv2.resize(image, (new_w, new_h), interpolation=cv2.INTER_AREA)
        
        return image
        
    @staticmethod
    def create_image_pyramid(image: np.ndarray, scales: list) -> list:
        """
        创建图像金字塔
        
        Args:
            image: 输入图像
            scales: 缩放比例列表
            
        Returns:
            图像金字塔列表
        """
        pyramid = []
        h, w = image.shape[:2]
        
        for scale in scales:
            if scale == 1.0:
                pyramid.append(image)
            else:
                new_w = int(w * scale)
                new_h = int(h * scale)
                if new_w > 0 and new_h > 0:
                    resized = cv2.resize(image, (new_w, new_h), interpolation=cv2.INTER_LINEAR)
                    pyramid.append(resized)
                    
        return pyramid
        
    @staticmethod
    def normalize_image(image: np.ndarray) -> np.ndarray:
        """
        归一化图像到[0, 1]范围
        
        Args:
            image: 输入图像
            
        Returns:
            归一化后的图像
        """
        return image.astype(np.float32) / 255.0
        
    @staticmethod
    def denormalize_image(image: np.ndarray) -> np.ndarray:
        """
        反归一化图像到[0, 255]范围
        
        Args:
            image: 归一化的图像
            
        Returns:
            反归一化后的图像
        """
        return (image * 255.0).astype(np.uint8)
