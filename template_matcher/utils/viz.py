"""
Visualization Module
可视化模块

提供模板匹配结果的可视化功能。
"""

import cv2
import numpy as np
import matplotlib.pyplot as plt
from typing import List, Tuple, Optional, Dict, Any
import os
import logging

logger = logging.getLogger(__name__)


class Visualizer:
    """可视化器"""
    
    def __init__(self, config: dict):
        """
        初始化可视化器
        
        Args:
            config: 可视化配置字典
        """
        self.config = config
        self.viz_config = config.get('visualization', {})
        self.output_dir = self.viz_config.get('output_dir', 'template_matcher/outputs')
        
        # 创建输出目录
        os.makedirs(self.output_dir, exist_ok=True)
        
    def draw_matches(self, 
                    template: np.ndarray,
                    target: np.ndarray,
                    template_kpts: List,
                    target_kpts: List,
                    matches: List,
                    title: str = "Feature Matches") -> np.ndarray:
        """
        绘制特征点匹配结果
        
        Args:
            template: 模板图像
            target: 目标图像
            template_kpts: 模板关键点
            target_kpts: 目标关键点
            matches: 匹配结果
            title: 图像标题
            
        Returns:
            匹配结果图像
        """
        draw_params = self.viz_config.get('draw_params', {})
        
        # 设置绘制参数
        match_color = tuple(draw_params.get('matchColor', [0, 255, 0]))
        single_point_color = tuple(draw_params.get('singlePointColor', [255, 0, 0]))
        flags = draw_params.get('flags', 2)
        
        # 绘制匹配
        match_img = cv2.drawMatches(
            template, template_kpts,
            target, target_kpts,
            matches, None,
            matchColor=match_color,
            singlePointColor=single_point_color,
            flags=flags
        )
        
        # 添加标题
        if title:
            cv2.putText(match_img, title, (10, 30), 
                       cv2.FONT_HERSHEY_SIMPLEX, 1, (255, 255, 255), 2)
            
        return match_img
        
    def draw_bounding_box(self, 
                         image: np.ndarray,
                         corners: np.ndarray,
                         color: Tuple[int, int, int] = (0, 255, 0),
                         thickness: int = 3) -> np.ndarray:
        """
        在图像上绘制边界框
        
        Args:
            image: 输入图像
            corners: 边界框角点 (4x2)
            color: 绘制颜色
            thickness: 线条粗细
            
        Returns:
            绘制后的图像
        """
        result = image.copy()
        
        # 确保corners是整数类型
        corners = corners.astype(np.int32)
        
        # 绘制边界框
        cv2.polylines(result, [corners], True, color, thickness)
        
        # 绘制角点
        for i, corner in enumerate(corners):
            cv2.circle(result, tuple(corner), 5, color, -1)
            cv2.putText(result, str(i), tuple(corner + 10), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.5, color, 1)
            
        return result
        
    def draw_template_match_result(self,
                                  template: np.ndarray,
                                  target: np.ndarray,
                                  match_result: Dict[str, Any],
                                  save_path: Optional[str] = None) -> np.ndarray:
        """
        绘制完整的模板匹配结果
        
        Args:
            template: 模板图像
            target: 目标图像
            match_result: 匹配结果字典
            save_path: 保存路径
            
        Returns:
            结果图像
        """
        # 创建结果图像
        if len(target.shape) == 2:
            target_color = cv2.cvtColor(target, cv2.COLOR_GRAY2BGR)
        else:
            target_color = target.copy()
            
        # 绘制检测到的模板位置
        if 'corners' in match_result:
            corners = match_result['corners']
            target_color = self.draw_bounding_box(target_color, corners)
            
        # 添加匹配信息
        info_text = []
        if 'confidence' in match_result:
            info_text.append(f"Confidence: {match_result['confidence']:.3f}")
        if 'num_inliers' in match_result:
            info_text.append(f"Inliers: {match_result['num_inliers']}")
        if 'homography' in match_result:
            info_text.append("Homography: Found")
            
        # 在图像上添加文本信息
        y_offset = 30
        for text in info_text:
            cv2.putText(target_color, text, (10, y_offset),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)
            y_offset += 30
            
        # 保存结果
        if save_path:
            cv2.imwrite(save_path, target_color)
            logger.info(f"Match result saved to: {save_path}")
            
        return target_color
        
    def create_comparison_view(self,
                              template: np.ndarray,
                              target: np.ndarray,
                              result: np.ndarray,
                              title: str = "Template Matching Result") -> np.ndarray:
        """
        创建对比视图
        
        Args:
            template: 模板图像
            target: 目标图像
            result: 结果图像
            title: 标题
            
        Returns:
            对比视图图像
        """
        # 调整图像大小使其高度一致
        h = max(template.shape[0], target.shape[0], result.shape[0])
        
        def resize_to_height(img, target_height):
            if len(img.shape) == 2:
                img = cv2.cvtColor(img, cv2.COLOR_GRAY2BGR)
            h, w = img.shape[:2]
            new_w = int(w * target_height / h)
            return cv2.resize(img, (new_w, target_height))
            
        template_resized = resize_to_height(template, h)
        target_resized = resize_to_height(target, h)
        result_resized = resize_to_height(result, h)
        
        # 水平拼接
        comparison = np.hstack([template_resized, target_resized, result_resized])
        
        # 添加标题
        cv2.putText(comparison, title, (10, 30),
                   cv2.FONT_HERSHEY_SIMPLEX, 1, (255, 255, 255), 2)
                   
        # 添加子图标签
        cv2.putText(comparison, "Template", (10, h - 10),
                   cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)
        cv2.putText(comparison, "Target", (template_resized.shape[1] + 10, h - 10),
                   cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)
        cv2.putText(comparison, "Result", (template_resized.shape[1] + target_resized.shape[1] + 10, h - 10),
                   cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)
        
        return comparison
        
    def show_image(self, image: np.ndarray, title: str = "Image", wait_key: bool = True):
        """
        显示图像
        
        Args:
            image: 要显示的图像
            title: 窗口标题
            wait_key: 是否等待按键
        """
        if self.viz_config.get('show_matches', True):
            cv2.imshow(title, image)
            if wait_key:
                cv2.waitKey(0)
                cv2.destroyAllWindows()
                
    def save_image(self, image: np.ndarray, filename: str) -> str:
        """
        保存图像
        
        Args:
            image: 要保存的图像
            filename: 文件名
            
        Returns:
            保存的完整路径
        """
        if self.viz_config.get('save_results', True):
            save_path = os.path.join(self.output_dir, filename)
            cv2.imwrite(save_path, image)
            logger.info(f"Image saved to: {save_path}")
            return save_path
        return ""
        
    def plot_confidence_scores(self, scores: List[float], save_path: Optional[str] = None):
        """
        绘制置信度分数图
        
        Args:
            scores: 置信度分数列表
            save_path: 保存路径
        """
        plt.figure(figsize=(10, 6))
        plt.plot(scores, 'b-', linewidth=2)
        plt.title('Template Matching Confidence Scores')
        plt.xlabel('Match Index')
        plt.ylabel('Confidence Score')
        plt.grid(True, alpha=0.3)
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            logger.info(f"Confidence plot saved to: {save_path}")
            
        if self.viz_config.get('show_matches', True):
            plt.show()
        else:
            plt.close()
