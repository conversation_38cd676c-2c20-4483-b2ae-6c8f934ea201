### 技术路线图一览

1. **总体思路**  
   先用“特征点匹配”解决 尺度/旋转/光照 等全局几何差异，再在候选 ROI 上做一次 **精细模板匹配**（归一化互相关）。这样既鲁棒又易于加速。  
2. **核心模块**  
   - 预处理与光照归一化  
   - 关键点检测 / 描述子匹配（SIFT / ORB）  
   - 单/多目标几何一致性验证（RANSAC–单应矩阵）  
   - ROI 金字塔-NCC 微调  
   - 结果评估与可视化  
3. **与实时性的折衷**  
   - 在线缓存模板特征；对目标图像做多线程金字塔分块  
   - 仅在置信度不足时启用更重的精调

---

#### 1. 算法选择

| 需求            | 推荐算法                               | 说明            |
| --------------- | -------------------------------------- | --------------- |
| 尺度/旋转不变性 | **SIFT / ORB**                         | SIFT 提供高精度 |
| 光照变化        | 直方图均衡 + SIFT“对比度/光照不变”特性 |                 |
| 轻微仿射/透视   | RANSAC 求单应矩阵；过滤误匹配          |                 |
| 亚像素微调      | 图像金字塔 + **归一化互相关 (NCC)**    |                 |
|                 |                                        |                 |
|                 |                                        |                 |

> OpenCV 自带 `matchTemplate` 只能处理平移，无法直接应对旋转缩放 。

---

#### 2. 依赖库与工具

```
opencv-python>=4.8     # SIFT/ORB、matchTemplate、CUDA 支持
numpy
scikit-image           # 备选 NCC、直方图均衡
```

---

#### 3. 代码架构设计

```
template_matcher/
├── config.yaml             # 参数
├── template_manager.py     # 读取/缓存模板特征
├── detector/
│   ├── keypoint_stage.py   # SIFT/ORB + FLANN/BruteForce
│   ├── geometric_filter.py # RANSAC 单应矩阵
│   └── ncc_refine.py       # 金字塔 NCC 微调
├── utils/
│   ├── preproc.py          # 直方图均衡 / gamma
│   └── viz.py              # 结果可视化
└── template_matcher.py     # 主接口类                  
```

---

#### 4. 实现步骤

1. **模板离线处理**  
   - 灰度化 + CLAHE / γ 校正  
   - SIFT/ORB 特征提取，写入 `.npz`，同时保存关键点相对模板坐标。  
2. **主循环**  
   1. 对输入帧预处理（归一化光照、缩放至多分辨率金字塔）。  
   2. 读取缓存模板特征，执行 KNN-Match（比值测试 0.75）。  
   3. RANSAC 单应验证 (`cv2.findHomography`)；内点数 > 阈值即认为定位成功。  
   4. 使用估计的单应矩阵把模板四角投影到目标图像得 ROI。  
   5. 在 ROI 上按 0.5 / 1.0 / 2.0 … 尺度做 `matchTemplate(method=cv2.TM_CCOEFF_NORMED)`； 置信度 > 0.8 则输出。    

---

#### 5. 结论

采用 **“ORB/SIFT 关键点 + RANSAC + ROI NCC 微调”** 的两阶段策略既能适应 **尺度、旋转、光照变化**，基础功能完全依赖 OpenCV/Python，易于部署。