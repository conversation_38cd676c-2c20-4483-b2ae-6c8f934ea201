"""
Template Manager Module
模板管理器模块

负责模板的加载、特征提取、缓存管理等功能。
"""

import cv2
import numpy as np
import os
import hashlib
from typing import Dict, List, Tuple, Optional, Any
import logging
from .utils.preproc import ImagePreprocessor

logger = logging.getLogger(__name__)


class TemplateManager:
    """模板管理器"""
    
    def __init__(self, config: dict):
        """
        初始化模板管理器
        
        Args:
            config: 配置字典
        """
        self.config = config
        self.template_config = config.get('template_manager', {})
        self.feature_config = config.get('feature_detector', {})
        
        # 缓存目录
        self.cache_dir = self.template_config.get('cache_dir', 'template_matcher/cache')
        os.makedirs(self.cache_dir, exist_ok=True)
        
        # 特征文件格式
        self.feature_format = self.template_config.get('feature_format', '.npz')
        
        # 是否强制重新计算
        self.force_recompute = self.template_config.get('force_recompute', False)
        
        # 初始化预处理器
        self.preprocessor = ImagePreprocessor(config)
        
        # 初始化特征检测器
        self.detector = self._create_feature_detector()
        
        # 模板缓存
        self.templates = {}
        
    def _create_feature_detector(self):
        """创建特征检测器"""
        algorithm = self.feature_config.get('algorithm', 'SIFT')
        
        if algorithm == 'SIFT':
            sift_config = self.feature_config.get('sift', {})
            return cv2.SIFT_create(
                nfeatures=sift_config.get('nfeatures', 0),
                nOctaveLayers=sift_config.get('nOctaveLayers', 3),
                contrastThreshold=sift_config.get('contrastThreshold', 0.04),
                edgeThreshold=sift_config.get('edgeThreshold', 10),
                sigma=sift_config.get('sigma', 1.6)
            )
        elif algorithm == 'ORB':
            orb_config = self.feature_config.get('orb', {})
            return cv2.ORB_create(
                nfeatures=orb_config.get('nfeatures', 500),
                scaleFactor=orb_config.get('scaleFactor', 1.2),
                nlevels=orb_config.get('nlevels', 8),
                edgeThreshold=orb_config.get('edgeThreshold', 31),
                firstLevel=orb_config.get('firstLevel', 0),
                WTA_K=orb_config.get('WTA_K', 2),
                scoreType=orb_config.get('scoreType', 0),
                patchSize=orb_config.get('patchSize', 31)
            )
        else:
            raise ValueError(f"Unsupported feature detector: {algorithm}")
            
    def load_template(self, template_path: str, template_id: Optional[str] = None) -> Dict[str, Any]:
        """
        加载模板
        
        Args:
            template_path: 模板图像路径
            template_id: 模板ID，如果为None则使用文件名
            
        Returns:
            模板信息字典
        """
        if template_id is None:
            template_id = os.path.splitext(os.path.basename(template_path))[0]
            
        # 检查是否已经加载
        if template_id in self.templates:
            logger.info(f"Template {template_id} already loaded")
            return self.templates[template_id]
            
        # 读取模板图像
        template_image = cv2.imread(template_path)
        if template_image is None:
            raise ValueError(f"Cannot load template image: {template_path}")
            
        # 预处理
        template_gray = self.preprocessor.preprocess(template_image)
        
        # 生成缓存文件路径
        cache_path = self._get_cache_path(template_path, template_id)
        
        # 尝试从缓存加载特征
        template_info = self._load_features_from_cache(cache_path, template_gray)
        
        if template_info is None or self.force_recompute:
            # 提取特征
            template_info = self._extract_template_features(template_gray, template_id)
            
            # 保存到缓存
            self._save_features_to_cache(cache_path, template_info)
            
        # 添加原始图像信息
        template_info.update({
            'template_id': template_id,
            'template_path': template_path,
            'template_image': template_image,
            'template_gray': template_gray,
            'shape': template_gray.shape
        })
        
        # 缓存模板信息
        self.templates[template_id] = template_info
        
        logger.info(f"Template {template_id} loaded successfully with {len(template_info['keypoints'])} keypoints")
        
        return template_info
        
    def _extract_template_features(self, template_gray: np.ndarray, template_id: str) -> Dict[str, Any]:
        """
        提取模板特征
        
        Args:
            template_gray: 预处理后的模板图像
            template_id: 模板ID
            
        Returns:
            特征信息字典
        """
        logger.info(f"Extracting features for template {template_id}")
        
        # 检测关键点和描述子
        keypoints, descriptors = self.detector.detectAndCompute(template_gray, None)
        
        if descriptors is None or len(keypoints) == 0:
            raise ValueError(f"No features detected in template {template_id}")
            
        # 转换关键点为可序列化格式
        kpts_data = []
        for kp in keypoints:
            kpts_data.append({
                'pt': kp.pt,
                'angle': kp.angle,
                'class_id': kp.class_id,
                'octave': kp.octave,
                'response': kp.response,
                'size': kp.size
            })
            
        return {
            'keypoints': keypoints,
            'descriptors': descriptors,
            'keypoints_data': kpts_data,
            'num_features': len(keypoints),
            'algorithm': self.feature_config.get('algorithm', 'SIFT')
        }
        
    def _get_cache_path(self, template_path: str, template_id: str) -> str:
        """
        生成缓存文件路径
        
        Args:
            template_path: 模板图像路径
            template_id: 模板ID
            
        Returns:
            缓存文件路径
        """
        # 生成基于文件内容和配置的哈希
        with open(template_path, 'rb') as f:
            file_content = f.read()
            
        config_str = str(sorted(self.feature_config.items()))
        hash_input = file_content + config_str.encode()
        file_hash = hashlib.md5(hash_input).hexdigest()[:8]
        
        cache_filename = f"{template_id}_{file_hash}{self.feature_format}"
        return os.path.join(self.cache_dir, cache_filename)
        
    def _load_features_from_cache(self, cache_path: str, template_gray: np.ndarray) -> Optional[Dict[str, Any]]:
        """
        从缓存加载特征
        
        Args:
            cache_path: 缓存文件路径
            template_gray: 模板图像（用于重建关键点）
            
        Returns:
            特征信息字典或None
        """
        if not os.path.exists(cache_path):
            return None
            
        try:
            logger.info(f"Loading features from cache: {cache_path}")
            data = np.load(cache_path, allow_pickle=True)
            
            # 重建关键点对象
            keypoints = []
            kpts_data = data['keypoints_data'].item()
            for kp_data in kpts_data:
                kp = cv2.KeyPoint(
                    x=kp_data['pt'][0],
                    y=kp_data['pt'][1],
                    size=kp_data['size'],
                    angle=kp_data['angle'],
                    response=kp_data['response'],
                    octave=kp_data['octave'],
                    class_id=kp_data['class_id']
                )
                keypoints.append(kp)
                
            return {
                'keypoints': keypoints,
                'descriptors': data['descriptors'],
                'keypoints_data': kpts_data,
                'num_features': len(keypoints),
                'algorithm': str(data['algorithm'])
            }
            
        except Exception as e:
            logger.warning(f"Failed to load features from cache: {e}")
            return None
            
    def _save_features_to_cache(self, cache_path: str, template_info: Dict[str, Any]):
        """
        保存特征到缓存
        
        Args:
            cache_path: 缓存文件路径
            template_info: 特征信息字典
        """
        try:
            logger.info(f"Saving features to cache: {cache_path}")
            np.savez_compressed(
                cache_path,
                descriptors=template_info['descriptors'],
                keypoints_data=template_info['keypoints_data'],
                num_features=template_info['num_features'],
                algorithm=template_info['algorithm']
            )
        except Exception as e:
            logger.warning(f"Failed to save features to cache: {e}")
            
    def get_template(self, template_id: str) -> Optional[Dict[str, Any]]:
        """
        获取模板信息
        
        Args:
            template_id: 模板ID
            
        Returns:
            模板信息字典或None
        """
        return self.templates.get(template_id)
        
    def list_templates(self) -> List[str]:
        """
        列出所有已加载的模板ID
        
        Returns:
            模板ID列表
        """
        return list(self.templates.keys())
        
    def remove_template(self, template_id: str) -> bool:
        """
        移除模板
        
        Args:
            template_id: 模板ID
            
        Returns:
            是否成功移除
        """
        if template_id in self.templates:
            del self.templates[template_id]
            logger.info(f"Template {template_id} removed")
            return True
        return False
        
    def clear_cache(self):
        """清空缓存目录"""
        import shutil
        if os.path.exists(self.cache_dir):
            shutil.rmtree(self.cache_dir)
            os.makedirs(self.cache_dir, exist_ok=True)
            logger.info("Template cache cleared")
