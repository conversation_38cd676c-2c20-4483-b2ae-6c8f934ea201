"""
Keypoint Detection and Matching Stage
关键点检测和匹配阶段

实现SIFT/ORB特征检测和FLANN/BruteForce匹配。
"""

import cv2
import numpy as np
from typing import List, Tuple, Dict, Any, Optional
import logging

logger = logging.getLogger(__name__)


class KeypointDetector:
    """关键点检测器"""
    
    def __init__(self, config: dict):
        """
        初始化关键点检测器
        
        Args:
            config: 配置字典
        """
        self.config = config
        self.feature_config = config.get('feature_detector', {})
        self.matcher_config = config.get('feature_matcher', {})
        
        # 创建特征检测器
        self.detector = self._create_feature_detector()
        
        # 创建特征匹配器
        self.matcher = self._create_feature_matcher()
        
    def _create_feature_detector(self):
        """创建特征检测器"""
        algorithm = self.feature_config.get('algorithm', 'SIFT')
        
        if algorithm == 'SIFT':
            sift_config = self.feature_config.get('sift', {})
            return cv2.SIFT_create(
                nfeatures=sift_config.get('nfeatures', 0),
                nOctaveLayers=sift_config.get('nOctaveLayers', 3),
                contrastThreshold=sift_config.get('contrastThreshold', 0.04),
                edgeThreshold=sift_config.get('edgeThreshold', 10),
                sigma=sift_config.get('sigma', 1.6)
            )
        elif algorithm == 'ORB':
            orb_config = self.feature_config.get('orb', {})
            return cv2.ORB_create(
                nfeatures=orb_config.get('nfeatures', 500),
                scaleFactor=orb_config.get('scaleFactor', 1.2),
                nlevels=orb_config.get('nlevels', 8),
                edgeThreshold=orb_config.get('edgeThreshold', 31),
                firstLevel=orb_config.get('firstLevel', 0),
                WTA_K=orb_config.get('WTA_K', 2),
                scoreType=orb_config.get('scoreType', 0),
                patchSize=orb_config.get('patchSize', 31)
            )
        else:
            raise ValueError(f"Unsupported feature detector: {algorithm}")
            
    def _create_feature_matcher(self):
        """创建特征匹配器"""
        matcher_type = self.matcher_config.get('matcher_type', 'FLANN')
        algorithm = self.feature_config.get('algorithm', 'SIFT')
        
        if matcher_type == 'FLANN':
            if algorithm == 'SIFT':
                # SIFT使用FLANN_INDEX_KDTREE
                flann_config = self.matcher_config.get('flann', {})
                index_params = dict(
                    algorithm=flann_config.get('algorithm', 1),  # FLANN_INDEX_KDTREE
                    trees=flann_config.get('trees', 5)
                )
                search_params = dict(
                    checks=flann_config.get('checks', 50)
                )
                return cv2.FlannBasedMatcher(index_params, search_params)
            else:
                # ORB使用FLANN_INDEX_LSH
                index_params = dict(
                    algorithm=6,  # FLANN_INDEX_LSH
                    table_number=6,
                    key_size=12,
                    multi_probe_level=1
                )
                search_params = dict(checks=50)
                return cv2.FlannBasedMatcher(index_params, search_params)
                
        elif matcher_type == 'BruteForce':
            bf_config = self.matcher_config.get('brute_force', {})
            if algorithm == 'SIFT':
                return cv2.BFMatcher(
                    normType=bf_config.get('normType', cv2.NORM_L2),
                    crossCheck=bf_config.get('crossCheck', True)
                )
            else:  # ORB
                return cv2.BFMatcher(
                    normType=cv2.NORM_HAMMING,
                    crossCheck=bf_config.get('crossCheck', True)
                )
        else:
            raise ValueError(f"Unsupported matcher type: {matcher_type}")
            
    def detect_and_compute(self, image: np.ndarray) -> Tuple[List, np.ndarray]:
        """
        检测关键点和计算描述子
        
        Args:
            image: 输入图像（灰度）
            
        Returns:
            (关键点列表, 描述子数组)
        """
        keypoints, descriptors = self.detector.detectAndCompute(image, None)
        
        if descriptors is None:
            logger.warning("No features detected in image")
            return [], np.array([])
            
        logger.debug(f"Detected {len(keypoints)} keypoints")
        return keypoints, descriptors
        
    def match_features(self, 
                      template_descriptors: np.ndarray,
                      target_descriptors: np.ndarray) -> List[cv2.DMatch]:
        """
        匹配特征描述子
        
        Args:
            template_descriptors: 模板描述子
            target_descriptors: 目标描述子
            
        Returns:
            匹配结果列表
        """
        if template_descriptors is None or target_descriptors is None:
            logger.warning("Cannot match features: descriptors are None")
            return []
            
        if len(template_descriptors) == 0 or len(target_descriptors) == 0:
            logger.warning("Cannot match features: empty descriptors")
            return []
            
        try:
            # 使用KNN匹配
            knn_config = self.matcher_config.get('knn', {})
            k = knn_config.get('k', 2)
            
            if self.matcher_config.get('matcher_type') == 'BruteForce' and \
               self.matcher_config.get('brute_force', {}).get('crossCheck', True):
                # 暴力匹配器启用交叉检查时，直接使用match方法
                matches = self.matcher.match(template_descriptors, target_descriptors)
                # 按距离排序
                matches = sorted(matches, key=lambda x: x.distance)
            else:
                # 使用KNN匹配
                knn_matches = self.matcher.knnMatch(template_descriptors, target_descriptors, k=k)
                
                # 应用Lowe's ratio test
                ratio_threshold = knn_config.get('ratio_threshold', 0.75)
                matches = []
                for match_pair in knn_matches:
                    if len(match_pair) == 2:
                        m, n = match_pair
                        if m.distance < ratio_threshold * n.distance:
                            matches.append(m)
                            
            logger.debug(f"Found {len(matches)} good matches")
            return matches
            
        except Exception as e:
            logger.error(f"Feature matching failed: {e}")
            return []
            
    def filter_matches_by_distance(self, 
                                  matches: List[cv2.DMatch],
                                  distance_threshold: Optional[float] = None) -> List[cv2.DMatch]:
        """
        根据距离过滤匹配
        
        Args:
            matches: 匹配结果列表
            distance_threshold: 距离阈值，如果为None则自动计算
            
        Returns:
            过滤后的匹配列表
        """
        if not matches:
            return matches
            
        if distance_threshold is None:
            # 自动计算阈值：平均距离的2倍
            distances = [m.distance for m in matches]
            distance_threshold = 2.0 * np.mean(distances)
            
        filtered_matches = [m for m in matches if m.distance < distance_threshold]
        
        logger.debug(f"Distance filtering: {len(matches)} -> {len(filtered_matches)} matches")
        return filtered_matches
        
    def extract_matched_points(self,
                              template_keypoints: List,
                              target_keypoints: List,
                              matches: List[cv2.DMatch]) -> Tuple[np.ndarray, np.ndarray]:
        """
        提取匹配的关键点坐标
        
        Args:
            template_keypoints: 模板关键点
            target_keypoints: 目标关键点
            matches: 匹配结果
            
        Returns:
            (模板点坐标, 目标点坐标)
        """
        if not matches:
            return np.array([]), np.array([])
            
        template_pts = np.float32([template_keypoints[m.queryIdx].pt for m in matches]).reshape(-1, 1, 2)
        target_pts = np.float32([target_keypoints[m.trainIdx].pt for m in matches]).reshape(-1, 1, 2)
        
        return template_pts, target_pts
        
    def detect_and_match(self,
                        template_image: np.ndarray,
                        target_image: np.ndarray,
                        template_descriptors: Optional[np.ndarray] = None) -> Dict[str, Any]:
        """
        完整的检测和匹配流程
        
        Args:
            template_image: 模板图像
            target_image: 目标图像
            template_descriptors: 预计算的模板描述子（可选）
            
        Returns:
            匹配结果字典
        """
        # 检测目标图像特征
        target_kpts, target_desc = self.detect_and_compute(target_image)
        
        if len(target_kpts) == 0:
            return {
                'matches': [],
                'template_keypoints': [],
                'target_keypoints': target_kpts,
                'template_points': np.array([]),
                'target_points': np.array([]),
                'num_matches': 0
            }
            
        # 如果没有提供模板描述子，则重新计算
        if template_descriptors is None:
            template_kpts, template_desc = self.detect_and_compute(template_image)
        else:
            # 假设模板关键点已经预计算
            template_kpts = []  # 需要从外部提供
            template_desc = template_descriptors
            
        # 匹配特征
        matches = self.match_features(template_desc, target_desc)
        
        # 过滤匹配
        matches = self.filter_matches_by_distance(matches)
        
        # 提取匹配点坐标
        template_pts, target_pts = self.extract_matched_points(template_kpts, target_kpts, matches)
        
        return {
            'matches': matches,
            'template_keypoints': template_kpts,
            'target_keypoints': target_kpts,
            'template_points': template_pts,
            'target_points': target_pts,
            'num_matches': len(matches)
        }
