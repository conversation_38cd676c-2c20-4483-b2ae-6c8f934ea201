"""
NCC (Normalized Cross-Correlation) Refinement
NCC精细匹配模块

在ROI区域进行多尺度金字塔归一化互相关匹配。
"""

import cv2
import numpy as np
from typing import Tuple, List, Dict, Any, Optional
import logging
from ..utils.preproc import ImagePreprocessor

logger = logging.getLogger(__name__)


class NCCRefiner:
    """NCC精细匹配器"""
    
    def __init__(self, config: dict):
        """
        初始化NCC精细匹配器
        
        Args:
            config: 配置字典
        """
        self.config = config
        self.ncc_config = config.get('ncc_refine', {})
        
        # 模板匹配方法
        self.method = self.ncc_config.get('method', cv2.TM_CCOEFF_NORMED)
        
        # 金字塔尺度
        self.pyramid_scales = self.ncc_config.get('pyramid_scales', [0.5, 1.0, 2.0])
        
        # 置信度阈值
        self.confidence_threshold = self.ncc_config.get('confidence_threshold', 0.8)
        
        # ROI扩展比例
        self.roi_expand_ratio = self.ncc_config.get('roi_expand_ratio', 0.1)
        
        # 预处理器
        self.preprocessor = ImagePreprocessor(config)
        
    def extract_roi_from_corners(self,
                                image: np.ndarray,
                                corners: np.ndarray,
                                expand_ratio: Optional[float] = None) -> Tuple[np.ndarray, Tuple[int, int, int, int]]:
        """
        从角点提取ROI区域
        
        Args:
            image: 输入图像
            corners: 四角坐标 (4, 2)
            expand_ratio: 扩展比例
            
        Returns:
            (ROI图像, (x, y, w, h))
        """
        if expand_ratio is None:
            expand_ratio = self.roi_expand_ratio
            
        # 计算边界框
        x_min = int(np.min(corners[:, 0]))
        y_min = int(np.min(corners[:, 1]))
        x_max = int(np.max(corners[:, 0]))
        y_max = int(np.max(corners[:, 1]))
        
        # 扩展边界框
        w = x_max - x_min
        h = y_max - y_min
        
        expand_w = int(w * expand_ratio)
        expand_h = int(h * expand_ratio)
        
        x_min = max(0, x_min - expand_w)
        y_min = max(0, y_min - expand_h)
        x_max = min(image.shape[1], x_max + expand_w)
        y_max = min(image.shape[0], y_max + expand_h)
        
        # 提取ROI
        roi = image[y_min:y_max, x_min:x_max]
        roi_bbox = (x_min, y_min, x_max - x_min, y_max - y_min)
        
        return roi, roi_bbox
        
    def template_match_single_scale(self,
                                   template: np.ndarray,
                                   roi: np.ndarray,
                                   scale: float = 1.0) -> Tuple[float, Tuple[int, int]]:
        """
        单尺度模板匹配
        
        Args:
            template: 模板图像
            roi: ROI区域
            scale: 缩放比例
            
        Returns:
            (最大置信度, 最佳匹配位置)
        """
        # 缩放模板
        if scale != 1.0:
            h, w = template.shape[:2]
            new_h, new_w = int(h * scale), int(w * scale)
            if new_h <= 0 or new_w <= 0:
                return 0.0, (0, 0)
            scaled_template = cv2.resize(template, (new_w, new_h), interpolation=cv2.INTER_LINEAR)
        else:
            scaled_template = template
            
        # 检查模板大小是否合适
        if scaled_template.shape[0] >= roi.shape[0] or scaled_template.shape[1] >= roi.shape[1]:
            return 0.0, (0, 0)
            
        try:
            # 模板匹配
            result = cv2.matchTemplate(roi, scaled_template, self.method)
            
            # 找到最佳匹配位置
            min_val, max_val, min_loc, max_loc = cv2.minMaxLoc(result)
            
            # 根据匹配方法选择最佳值和位置
            if self.method in [cv2.TM_SQDIFF, cv2.TM_SQDIFF_NORMED]:
                best_val = 1.0 - min_val if self.method == cv2.TM_SQDIFF_NORMED else -min_val
                best_loc = min_loc
            else:
                best_val = max_val
                best_loc = max_loc
                
            return best_val, best_loc
            
        except Exception as e:
            logger.error(f"Template matching failed at scale {scale}: {e}")
            return 0.0, (0, 0)
            
    def template_match_multiscale(self,
                                 template: np.ndarray,
                                 roi: np.ndarray,
                                 scales: Optional[List[float]] = None) -> Dict[str, Any]:
        """
        多尺度模板匹配
        
        Args:
            template: 模板图像
            roi: ROI区域
            scales: 尺度列表
            
        Returns:
            匹配结果字典
        """
        if scales is None:
            scales = self.pyramid_scales
            
        best_confidence = 0.0
        best_location = (0, 0)
        best_scale = 1.0
        scale_results = []
        
        for scale in scales:
            confidence, location = self.template_match_single_scale(template, roi, scale)
            
            scale_results.append({
                'scale': scale,
                'confidence': confidence,
                'location': location
            })
            
            if confidence > best_confidence:
                best_confidence = confidence
                best_location = location
                best_scale = scale
                
            logger.debug(f"Scale {scale}: confidence={confidence:.3f}, location={location}")
            
        return {
            'best_confidence': best_confidence,
            'best_location': best_location,
            'best_scale': best_scale,
            'scale_results': scale_results,
            'is_confident': best_confidence >= self.confidence_threshold
        }
        
    def refine_match_location(self,
                            template: np.ndarray,
                            roi: np.ndarray,
                            initial_location: Tuple[int, int],
                            scale: float = 1.0,
                            search_radius: int = 5) -> Tuple[float, Tuple[int, int]]:
        """
        精细化匹配位置
        
        Args:
            template: 模板图像
            roi: ROI区域
            initial_location: 初始位置
            scale: 尺度
            search_radius: 搜索半径
            
        Returns:
            (精细化置信度, 精细化位置)
        """
        x0, y0 = initial_location
        
        # 缩放模板
        if scale != 1.0:
            h, w = template.shape[:2]
            new_h, new_w = int(h * scale), int(w * scale)
            if new_h <= 0 or new_w <= 0:
                return 0.0, initial_location
            scaled_template = cv2.resize(template, (new_w, new_h), interpolation=cv2.INTER_LINEAR)
        else:
            scaled_template = template
            
        best_confidence = 0.0
        best_location = initial_location
        
        # 在搜索半径内寻找最佳位置
        for dy in range(-search_radius, search_radius + 1):
            for dx in range(-search_radius, search_radius + 1):
                x, y = x0 + dx, y0 + dy
                
                # 检查边界
                if (x < 0 or y < 0 or 
                    x + scaled_template.shape[1] >= roi.shape[1] or 
                    y + scaled_template.shape[0] >= roi.shape[0]):
                    continue
                    
                # 提取对应区域
                roi_patch = roi[y:y + scaled_template.shape[0], x:x + scaled_template.shape[1]]
                
                # 计算归一化互相关
                try:
                    result = cv2.matchTemplate(roi_patch, scaled_template, cv2.TM_CCOEFF_NORMED)
                    confidence = result[0, 0]
                    
                    if confidence > best_confidence:
                        best_confidence = confidence
                        best_location = (x, y)
                        
                except Exception:
                    continue
                    
        return best_confidence, best_location
        
    def ncc_refine(self,
                  template: np.ndarray,
                  target_image: np.ndarray,
                  corners: np.ndarray,
                  enable_refinement: bool = True) -> Dict[str, Any]:
        """
        NCC精细匹配主函数
        
        Args:
            template: 模板图像
            target_image: 目标图像
            corners: 粗匹配得到的四角坐标
            enable_refinement: 是否启用位置精细化
            
        Returns:
            精细匹配结果字典
        """
        # 提取ROI
        roi, roi_bbox = self.extract_roi_from_corners(target_image, corners)
        
        if roi.size == 0:
            logger.warning("Empty ROI extracted")
            return {
                'success': False,
                'confidence': 0.0,
                'location': (0, 0),
                'roi_bbox': roi_bbox,
                'refined_corners': corners
            }
            
        # 多尺度模板匹配
        match_result = self.template_match_multiscale(template, roi)
        
        if not match_result['is_confident']:
            logger.warning(f"Low confidence match: {match_result['best_confidence']:.3f}")
            
        # 位置精细化
        if enable_refinement and match_result['is_confident']:
            refined_confidence, refined_location = self.refine_match_location(
                template, roi, match_result['best_location'], match_result['best_scale']
            )
            
            if refined_confidence > match_result['best_confidence']:
                match_result['best_confidence'] = refined_confidence
                match_result['best_location'] = refined_location
                logger.debug(f"Refined match: confidence={refined_confidence:.3f}")
                
        # 计算在原图中的位置
        roi_x, roi_y = roi_bbox[:2]
        match_x, match_y = match_result['best_location']
        global_location = (roi_x + match_x, roi_y + match_y)
        
        # 计算精细化后的角点
        if match_result['is_confident']:
            # 基于最佳匹配位置和尺度计算新的角点
            scale = match_result['best_scale']
            h, w = template.shape[:2]
            scaled_h, scaled_w = int(h * scale), int(w * scale)
            
            x, y = global_location
            refined_corners = np.array([
                [x, y],
                [x + scaled_w, y],
                [x + scaled_w, y + scaled_h],
                [x, y + scaled_h]
            ], dtype=np.float32)
        else:
            refined_corners = corners
            
        return {
            'success': match_result['is_confident'],
            'confidence': match_result['best_confidence'],
            'location': global_location,
            'scale': match_result['best_scale'],
            'roi_bbox': roi_bbox,
            'refined_corners': refined_corners,
            'scale_results': match_result['scale_results']
        }
