"""
Geometric Consistency Filter
几何一致性过滤器

使用RANSAC算法进行单应矩阵估计和误匹配过滤。
"""

import cv2
import numpy as np
from typing import Tuple, Optional, Dict, Any, List
import logging

logger = logging.getLogger(__name__)


class GeometricFilter:
    """几何一致性过滤器"""
    
    def __init__(self, config: dict):
        """
        初始化几何过滤器
        
        Args:
            config: 配置字典
        """
        self.config = config
        self.geometric_config = config.get('geometric_filter', {})
        self.ransac_config = self.geometric_config.get('ransac', {})
        
        # RANSAC参数
        self.method = self.ransac_config.get('method', cv2.RANSAC)
        self.ransac_reproj_threshold = self.ransac_config.get('ransacReprojThreshold', 5.0)
        self.max_iters = self.ransac_config.get('maxIters', 2000)
        self.confidence = self.ransac_config.get('confidence', 0.99)
        
        # 最小内点数量
        self.min_inliers = self.geometric_config.get('min_inliers', 10)
        
    def find_homography(self,
                       template_points: np.ndarray,
                       target_points: np.ndarray) -> Tuple[Optional[np.ndarray], Optional[np.ndarray]]:
        """
        使用RANSAC估计单应矩阵
        
        Args:
            template_points: 模板点坐标 (N, 1, 2)
            target_points: 目标点坐标 (N, 1, 2)
            
        Returns:
            (单应矩阵, 内点掩码)
        """
        if len(template_points) < 4 or len(target_points) < 4:
            logger.warning("Need at least 4 point correspondences for homography estimation")
            return None, None
            
        try:
            homography, mask = cv2.findHomography(
                template_points,
                target_points,
                method=self.method,
                ransacReprojThreshold=self.ransac_reproj_threshold,
                maxIters=self.max_iters,
                confidence=self.confidence
            )
            
            if homography is None:
                logger.warning("Failed to find homography")
                return None, None
                
            # 检查内点数量
            num_inliers = np.sum(mask) if mask is not None else 0
            
            if num_inliers < self.min_inliers:
                logger.warning(f"Too few inliers: {num_inliers} < {self.min_inliers}")
                return None, None
                
            logger.debug(f"Found homography with {num_inliers} inliers")
            return homography, mask
            
        except Exception as e:
            logger.error(f"Homography estimation failed: {e}")
            return None, None
            
    def filter_matches_by_homography(self,
                                   matches: List[cv2.DMatch],
                                   template_keypoints: List,
                                   target_keypoints: List) -> Tuple[List[cv2.DMatch], Optional[np.ndarray], Optional[np.ndarray]]:
        """
        使用单应矩阵过滤匹配
        
        Args:
            matches: 原始匹配列表
            template_keypoints: 模板关键点
            target_keypoints: 目标关键点
            
        Returns:
            (过滤后的匹配, 单应矩阵, 内点掩码)
        """
        if not matches:
            return [], None, None
            
        # 提取匹配点坐标
        template_pts = np.float32([template_keypoints[m.queryIdx].pt for m in matches]).reshape(-1, 1, 2)
        target_pts = np.float32([target_keypoints[m.trainIdx].pt for m in matches]).reshape(-1, 1, 2)
        
        # 估计单应矩阵
        homography, mask = self.find_homography(template_pts, target_pts)
        
        if homography is None or mask is None:
            return [], None, None
            
        # 过滤匹配
        filtered_matches = [matches[i] for i in range(len(matches)) if mask[i]]
        
        logger.debug(f"Geometric filtering: {len(matches)} -> {len(filtered_matches)} matches")
        
        return filtered_matches, homography, mask
        
    def project_template_corners(self,
                               homography: np.ndarray,
                               template_shape: Tuple[int, int]) -> np.ndarray:
        """
        将模板四角投影到目标图像
        
        Args:
            homography: 单应矩阵
            template_shape: 模板图像形状 (height, width)
            
        Returns:
            投影后的四角坐标 (4, 2)
        """
        h, w = template_shape
        
        # 模板四角坐标
        template_corners = np.float32([
            [0, 0],
            [w, 0],
            [w, h],
            [0, h]
        ]).reshape(-1, 1, 2)
        
        # 投影到目标图像
        projected_corners = cv2.perspectiveTransform(template_corners, homography)
        
        return projected_corners.reshape(-1, 2)
        
    def validate_homography(self, homography: np.ndarray, template_shape: Tuple[int, int]) -> bool:
        """
        验证单应矩阵的合理性
        
        Args:
            homography: 单应矩阵
            template_shape: 模板图像形状
            
        Returns:
            是否合理
        """
        try:
            # 检查矩阵条件数
            cond_num = np.linalg.cond(homography)
            if cond_num > 1e6:
                logger.warning(f"Homography condition number too high: {cond_num}")
                return False
                
            # 检查投影后的四角形状
            corners = self.project_template_corners(homography, template_shape)
            
            # 检查是否形成凸四边形
            if not self._is_convex_quadrilateral(corners):
                logger.warning("Projected corners do not form a convex quadrilateral")
                return False
                
            # 检查面积比例
            original_area = template_shape[0] * template_shape[1]
            projected_area = cv2.contourArea(corners.astype(np.int32))
            
            area_ratio = projected_area / original_area
            if area_ratio < 0.1 or area_ratio > 10.0:
                logger.warning(f"Area ratio out of reasonable range: {area_ratio}")
                return False
                
            return True
            
        except Exception as e:
            logger.warning(f"Homography validation failed: {e}")
            return False
            
    def _is_convex_quadrilateral(self, corners: np.ndarray) -> bool:
        """
        检查四个点是否形成凸四边形
        
        Args:
            corners: 四角坐标 (4, 2)
            
        Returns:
            是否为凸四边形
        """
        def cross_product(o, a, b):
            return (a[0] - o[0]) * (b[1] - o[1]) - (a[1] - o[1]) * (b[0] - o[0])
            
        # 检查所有相邻三点的叉积符号是否一致
        n = len(corners)
        signs = []
        
        for i in range(n):
            o = corners[i]
            a = corners[(i + 1) % n]
            b = corners[(i + 2) % n]
            cp = cross_product(o, a, b)
            if abs(cp) > 1e-6:  # 避免数值误差
                signs.append(cp > 0)
                
        # 所有符号应该相同
        return len(set(signs)) <= 1
        
    def estimate_pose_from_homography(self,
                                    homography: np.ndarray,
                                    camera_matrix: np.ndarray,
                                    template_size: Tuple[float, float]) -> Tuple[Optional[np.ndarray], Optional[np.ndarray]]:
        """
        从单应矩阵估计姿态
        
        Args:
            homography: 单应矩阵
            camera_matrix: 相机内参矩阵
            template_size: 模板实际尺寸 (width, height) in meters
            
        Returns:
            (旋转向量, 平移向量)
        """
        try:
            # 定义模板在世界坐标系中的3D点（Z=0平面）
            w, h = template_size
            object_points = np.array([
                [0, 0, 0],
                [w, 0, 0],
                [w, h, 0],
                [0, h, 0]
            ], dtype=np.float32)
            
            # 从单应矩阵分解姿态
            num_solutions, rvecs, tvecs, normals = cv2.solvePnPGeneric(
                object_points,
                homography[:4, :2],  # 使用前4个对应点
                camera_matrix,
                None,  # 无畸变
                flags=cv2.SOLVEPNP_IPPE
            )
            
            if num_solutions > 0:
                # 选择第一个解
                return rvecs[0], tvecs[0]
            else:
                return None, None
                
        except Exception as e:
            logger.error(f"Pose estimation from homography failed: {e}")
            return None, None
            
    def geometric_verification(self,
                             matches: List[cv2.DMatch],
                             template_keypoints: List,
                             target_keypoints: List,
                             template_shape: Tuple[int, int]) -> Dict[str, Any]:
        """
        完整的几何验证流程
        
        Args:
            matches: 匹配列表
            template_keypoints: 模板关键点
            target_keypoints: 目标关键点
            template_shape: 模板图像形状
            
        Returns:
            验证结果字典
        """
        # 使用单应矩阵过滤匹配
        filtered_matches, homography, mask = self.filter_matches_by_homography(
            matches, template_keypoints, target_keypoints
        )
        
        result = {
            'filtered_matches': filtered_matches,
            'homography': homography,
            'inlier_mask': mask,
            'num_inliers': len(filtered_matches),
            'is_valid': False,
            'corners': None
        }
        
        if homography is not None:
            # 验证单应矩阵
            is_valid = self.validate_homography(homography, template_shape)
            result['is_valid'] = is_valid
            
            if is_valid:
                # 计算投影角点
                corners = self.project_template_corners(homography, template_shape)
                result['corners'] = corners
                
        return result
