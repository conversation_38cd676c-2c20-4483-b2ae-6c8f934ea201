# Template Matcher Configuration
# 模板匹配器配置文件

# 特征检测器配置
feature_detector:
  # 特征检测算法: 'SIFT' 或 'ORB'
  algorithm: 'SIFT'
  
  # SIFT 参数
  sift:
    nfeatures: 0          # 最大特征点数量 (0=无限制)
    nOctaveLayers: 3      # 每个八度的层数
    contrastThreshold: 0.04  # 对比度阈值
    edgeThreshold: 10     # 边缘阈值
    sigma: 1.6           # 高斯核标准差
  
  # ORB 参数
  orb:
    nfeatures: 500       # 最大特征点数量
    scaleFactor: 1.2     # 金字塔缩放因子
    nlevels: 8           # 金字塔层数
    edgeThreshold: 31    # 边缘阈值
    firstLevel: 0        # 第一层级别
    WTA_K: 2            # 每个描述子元素的点数
    scoreType: 0         # 评分类型 (0=HARRIS_SCORE, 1=FAST_SCORE)
    patchSize: 31        # 补丁大小

# 特征匹配器配置
feature_matcher:
  # 匹配器类型: 'FLANN' 或 'BruteForce'
  matcher_type: 'FLANN'
  
  # FLANN 参数
  flann:
    algorithm: 1         # FLANN_INDEX_KDTREE
    trees: 5            # KD树数量
    checks: 50          # 检查次数
  
  # 暴力匹配器参数
  brute_force:
    normType: 2         # cv2.NORM_L2
    crossCheck: true    # 交叉检查
  
  # KNN匹配参数
  knn:
    k: 2               # K值
    ratio_threshold: 0.75  # Lowe's ratio test阈值

# 几何验证配置
geometric_filter:
  # RANSAC参数
  ransac:
    method: 8          # cv2.RANSAC
    ransacReprojThreshold: 5.0  # 重投影误差阈值
    maxIters: 2000     # 最大迭代次数
    confidence: 0.99   # 置信度
  
  # 最小内点数量
  min_inliers: 10

# NCC精细匹配配置
ncc_refine:
  # 模板匹配方法
  method: 5            # cv2.TM_CCOEFF_NORMED
  
  # 金字塔尺度
  pyramid_scales: [0.5, 1.0, 2.0]
  
  # 置信度阈值
  confidence_threshold: 0.8
  
  # ROI扩展比例
  roi_expand_ratio: 0.1

# 图像预处理配置
preprocessing:
  # 光照归一化
  illumination:
    enable: true
    method: 'CLAHE'    # 'CLAHE', 'histogram_eq', 'gamma'
    
    # CLAHE参数
    clahe:
      clipLimit: 2.0
      tileGridSize: [8, 8]
    
    # Gamma校正参数
    gamma:
      gamma_value: 1.2
  
  # 图像缩放
  resize:
    enable: false
    max_width: 1024
    max_height: 768

# 模板管理配置
template_manager:
  # 模板特征缓存目录
  cache_dir: 'template_matcher/cache'
  
  # 特征文件格式
  feature_format: '.npz'
  
  # 是否强制重新提取特征
  force_recompute: false

# 可视化配置
visualization:
  # 是否显示匹配结果
  show_matches: true
  
  # 是否保存结果图像
  save_results: true
  
  # 结果保存目录
  output_dir: 'template_matcher/outputs'
  
  # 绘制参数
  draw_params:
    matchColor: [0, 255, 0]     # 匹配线颜色 (BGR)
    singlePointColor: [255, 0, 0]  # 单点颜色 (BGR)
    matchesMask: null           # 匹配掩码
    flags: 2                    # 绘制标志

# 性能配置
performance:
  # 是否使用多线程
  use_multithreading: true
  
  # 线程数量 (0=自动检测)
  num_threads: 0
  
  # 是否启用GPU加速 (需要OpenCV CUDA支持)
  use_gpu: false

# 调试配置
debug:
  # 调试级别: 'DEBUG', 'INFO', 'WARNING', 'ERROR'
  log_level: 'INFO'
  
  # 是否保存中间结果
  save_intermediate: false
  
  # 中间结果保存目录
  intermediate_dir: 'template_matcher/debug'
